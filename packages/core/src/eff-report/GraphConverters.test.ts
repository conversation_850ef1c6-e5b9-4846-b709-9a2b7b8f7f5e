import { convertPieChartToECharts } from "./GraphConverters";
import type { PieChartParams } from "./GraphSchemas";

describe("convertPieChartToECharts", () => {
	it("should group small slices into 'Other' category when they total ≤5%", () => {
		const params: PieChartParams = {
			title: "Test Pie Chart",
			explanation: "Test explanation",
			series: [
				{ name: "Large A", value: 50 },
				{ name: "Large B", value: 30 },
				{ name: "Medium", value: 15 },
				{ name: "Small 1", value: 2 },
				{ name: "Small 2", value: 2 },
				{ name: "Small 3", value: 1 },
			],
			seriesUnits: "units",
		};

		const result = convertPieChartToECharts(params);
		const series = Array.isArray(result.series) ? result.series : [result.series];
		const seriesData = series[0]?.data as Array<{ name: string; value: number }>;

		// Total is 100, so 5% threshold is 5
		// Small slices (2+2+1=5) should be grouped into "Other"
		expect(seriesData).toHaveLength(4); // Large A, Large B, Medium, Other
		expect(seriesData.map(item => item.name)).toContain("Other");

		const otherSlice = seriesData.find(item => item.name === "Other");
		expect(otherSlice?.value).toBe(5); // 2+2+1
	});

	it("should not group slices when small slices exceed 5% threshold", () => {
		const params: PieChartParams = {
			title: "Test Pie Chart",
			explanation: "Test explanation",
			series: [
				{ name: "Large A", value: 80 },
				{ name: "Large B", value: 14 },
				{ name: "Small 1", value: 4 },
				{ name: "Small 2", value: 2 },
			],
			seriesUnits: "units",
		};

		const result = convertPieChartToECharts(params);
		const series = Array.isArray(result.series) ? result.series : [result.series];
		const seriesData = series[0]?.data as Array<{ name: string; value: number }>;

		// Total is 100, small slices total 6 which is > 5%, so no grouping
		expect(seriesData).toHaveLength(4); // All original slices
		expect(seriesData.map(item => item.name)).not.toContain("Other");
	});

	it("should not group when there's only one small slice", () => {
		const params: PieChartParams = {
			title: "Test Pie Chart",
			explanation: "Test explanation",
			series: [
				{ name: "Large A", value: 80 },
				{ name: "Large B", value: 15 },
				{ name: "Small", value: 5 },
			],
			seriesUnits: "units",
		};

		const result = convertPieChartToECharts(params);
		const series = Array.isArray(result.series) ? result.series : [result.series];
		const seriesData = series[0]?.data as Array<{ name: string; value: number }>;

		// Only one small slice, so no grouping should occur
		expect(seriesData).toHaveLength(3); // All original slices
		expect(seriesData.map(item => item.name)).not.toContain("Other");
	});

	it("should handle edge case where exactly 5% should be grouped", () => {
		const params: PieChartParams = {
			title: "Test Pie Chart",
			explanation: "Test explanation",
			series: [
				{ name: "Large", value: 95 },
				{ name: "Small 1", value: 3 },
				{ name: "Small 2", value: 2 },
			],
			seriesUnits: "units",
		};

		const result = convertPieChartToECharts(params);
		const series = Array.isArray(result.series) ? result.series : [result.series];
		const seriesData = series[0]?.data as Array<{ name: string; value: number }>;

		// Total is 100, small slices total exactly 5%, should be grouped
		expect(seriesData).toHaveLength(2); // Large, Other
		expect(seriesData.map(item => item.name)).toContain("Other");

		const otherSlice = seriesData.find(item => item.name === "Other");
		expect(otherSlice?.value).toBe(5); // 3+2
	});

	it("should preserve original order for non-grouped slices", () => {
		const params: PieChartParams = {
			title: "Test Pie Chart",
			explanation: "Test explanation",
			series: [
				{ name: "Medium", value: 45 },
				{ name: "Large", value: 50 },
				{ name: "Small 1", value: 3 },
				{ name: "Small 2", value: 2 },
			],
			seriesUnits: "units",
		};

		const result = convertPieChartToECharts(params);
		const series = Array.isArray(result.series) ? result.series : [result.series];
		const seriesData = series[0]?.data as Array<{ name: string; value: number }>;

		// Should be sorted by value descending, with Other at the end
		expect(seriesData[0].name).toBe("Large"); // 50
		expect(seriesData[1].name).toBe("Medium"); // 45
		expect(seriesData[2].name).toBe("Other"); // 5 (3+2)
	});

	it("should round Other value to two decimal places", () => {
		const params: PieChartParams = {
			title: "Test Pie Chart",
			explanation: "Test explanation",
			series: [
				{ name: "Large", value: 95.123 },
				{ name: "Small 1", value: 2.456 },
				{ name: "Small 2", value: 2.567 },
			],
			seriesUnits: "units",
		};

		const result = convertPieChartToECharts(params);
		const series = Array.isArray(result.series) ? result.series : [result.series];
		const seriesData = series[0]?.data as Array<{ name: string; value: number }>;

		// Total is 100.146, small slices total 5.023 which is ≤5%, should be grouped and rounded
		expect(seriesData).toHaveLength(2); // Large, Other
		expect(seriesData.map(item => item.name)).toContain("Other");

		const otherSlice = seriesData.find(item => item.name === "Other");
		expect(otherSlice?.value).toBe(5.02); // 2.456 + 2.567 = 5.023, rounded to 5.02
	});
});
